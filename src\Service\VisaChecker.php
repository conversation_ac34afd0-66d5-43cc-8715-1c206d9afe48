<?php

namespace App\Service;

use App\Entity\Document;
use App\Repository\VisaRepository;

class VisaChecker
{
    private VisaRepository $visaRepository;
    private array $validatedOptionalVisas = [];

    public function __construct(VisaRepository $visaRepository)
    {
        $this->visaRepository = $visaRepository;
    }

    public function areVisasValidForStep(Document $document, string $step): bool
    {
        // Récupérer les visas validés pour le document
        // $validatedVisas = $this->visaRepository->findBy([
        //     'releasedDrawing' => $document->getId(),
        //     'status' => 'valid'
        // ]);
        $validatedVisas = $document->getVisasValid();

        // Charger les règles de validation pour l'étape
        $rules = $this->getVisaRulesForStep($step);

        // Appliquer les règles de validation
        return $this->applyVisaRules($rules, $validatedVisas);
    }

    private function applyVisaRules(array $rules, array $validatedVisas): bool
    {
        $this->validatedOptionalVisas = []; // Réinitialiser la liste des visas optionnels validés
    
        // Vérifier les visas requis (AND)
        if (!empty($rules['required'])) {
            foreach ($rules['required'] as $requiredVisa) {
                if (!$this->hasVisa($validatedVisas, $requiredVisa)) {
                    return false; // Échec si un visa requis est manquant
                }
            }
        }
    
        // Vérifier l'absence de certains visas
        if (!empty($rules['forbidden'])) {
            foreach ($rules['forbidden'] as $forbiddenVisa) {
                if ($this->hasVisa($validatedVisas, $forbiddenVisa)) {
                    return false; // Échec si un visa interdit est présent
                }
            }
        }
    
        // Vérifier les visas optionnels pour chaque condition
        if (!empty($rules['optional_or'])) {
            $found = false;
            foreach ($rules['optional_or'] as $optionalVisa) {
                if ($this->hasVisa($validatedVisas, $optionalVisa)) {
                    $this->validatedOptionalVisas[] = $optionalVisa; // Stocker les visas validés
                    $found = true;
                }
            }
            if (!$found) {
                return false; // Échec si aucun des visas optionnels n'est présent
            }
        }
    
        return true; // Toutes les règles ont été respectées
    }
    
    public function getValidatedOptionalVisas(): array
    {
        return $this->validatedOptionalVisas;
    }


    private function hasVisa(array $validatedVisas, string $visaName): bool
    {
        foreach ($validatedVisas as $validatedVisa) {
            if ($validatedVisa->getName() === $visaName) {
                return true;
            }
        }
        return false;
    }

    private function getVisaRulesForStep(string $step): array
    {
        // Configuration des règles de visas pour chaque étape
        $visaRules = [
            'BE_1' => [
                'required' => ['visa_BE_0'],
                'forbidden' => ['visa_BE_1'],
                'optional_or' => [],
            ],
            'BE' => [
                'required' => ['visa_BE_1'],
                'forbidden' => ['visa_BE'],
                'optional_or' => [],
            ],
            'Produit' => [
                'required' => ['visa_BE'],
                'forbidden' => ['visa_produit'],
                'optional_or' => [],
            ],
            'Project' => [
                'required' => ['visa_BE'],
                'forbidden' => ['visa_Project'],
                'optional_or' => [],
            ],
            
            'Quality' => [
                'required' => ['visa_BE'],
                'forbidden' => ['visa_Quality'],
                'optional_or' => [],
            ],
            'Qual_Logistique' => [
                'required' => ['visa_BE'],
                'forbidden' => ['visa_Qual_Logistique'],
                'optional_or' => [],
            ],
            'Logistique' => [
                'required' => ['visa_Qual_Logistique'],
                'forbidden' => ['visa_Logistique'],
                'optional_or' => [],
            ],


            'Achat_Rfq' => [
                'required' => ['visa_Quality', 'visa_Produit'],
                'forbidden' => ['visa_Achat_Rfq'],
                'optional_or' => [],
            ],

            'Achat_RoHs_REACH' => [
                'required' => ['visa_Achat_Rfq'],
                'forbidden' => ['visa_Achat_RoHs_REACH'],
                'optional_or' => [],
            ],
            
            'Achat_F30' => [
                'required' => ['visa_Achat_Rfq'],
                'forbidden' => ['visa_Achat_F30'],
                'optional_or' => [],
            ],

            'Assembly' => [
                'required' => ['visa_Produit'],
                'forbidden' => ['visa_prod'],
                'optional_or' => [],
            ],
            
            'Methode_assemblage' => [
                'required' => ['visa_prod' ],
                'forbidden' => ['visa_Methode_assemblage'],
                'optional_or' => [],
            ],

            'Machining' => [
                'required' => ['visa_Produit'],
                'forbidden' => ['visa_prod'],
                'optional_or' => [],
            ],

            'Molding' => [
                'required' => ['visa_Produit'],
                'forbidden' => ['visa_prod'],
                'optional_or' => [],
            ],

            'Planning' => [
                'required' => ['visa_prod'],
                'forbidden' => ['visa_Planning'],
                'optional_or' => [],
            ],

            'Metro' => [
                'required' => [],
                'forbidden' => ['visa_Metro'],
                'optional_or' => ['visa_prod', 'visa_Quality'], 
            ],


            'Achat_FIA' => [
                'required' => ['visa_Achat_Rfq', 'visa_Achat_F30'],
                'forbidden' => ['visa_Achat_FIA'],
                'optional_or' => [],
            ],

            'Achat_Hts' => [
                'required' => ['visa_Achat_FIA'],
                'forbidden' => ['visa_Achat_Hts'],
                'optional_or' => [],
            ],

            'SaisieHts' => [
                'required' => ['visa_hts'],
                'forbidden' => ['visa_SaisieHts'],
                'optional_or' => [],
            ],

            'methode_Labo' => [
                'required' => ['visa_Indus'],
                'forbidden' => ['visa_methode_Labo'],
                'optional_or' => [],
            ],

            'QProd' => [
                'required' => ['visa_Metro'],
                'forbidden' => ['visa_QProd'],
                'optional_or' => [],
            ],

            'Indus' => [
                'required' => ['visa_prod'],
                'forbidden' => ['visa_Indus'],
                'optional_or' => ['visa_Metro'],
            ],

            'Core_Data' => [
                'required' => [],
                'forbidden' => ['visa_Core_Data'],
                'optional_or' => ['visa_Achat_Rfq','visa_Achat_F30','visa_Metro','visa_Planning','visa_Quality','visa_Logistique'],
            ],

            'Prod_Data' => [
                'required' => ['visa_Core_Data'],
                'forbidden' => ['visa_Prod_Data'],
                'optional_or' => [],
            ],

            'Costing' => [
                'required' => [],
                'forbidden' => ['visa_Costing'],
                'optional_or' => ['visa_GID','visa_Achat_FIA'],
            ],

            'GID' => [
                'required' => ['visa_Prod_Data'],
                'forbidden' => ['visa_GID'],
                'optional_or' => ['visa_prod','visa_Indus'],
            ],

            'Tirage_Plans' => [
                'required' => ['visa_Core_Data'],
                'forbidden' => ['visa_Tirage_Plans'],
                'optional_or' => [],
            ],
        ];

        return $visaRules[$step] ?? [];
    }
}
